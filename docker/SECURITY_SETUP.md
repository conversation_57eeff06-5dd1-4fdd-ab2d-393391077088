# Supabase Docker 安全配置指南

## 已更新的安全配置

### 1. 数据库密码
- **POSTGRES_PASSWORD**: 已设置为强密码 `Sup4b4se_P0stgr3s_P4ssw0rd_2024!`
- 包含大小写字母、数字和特殊字符

### 2. JWT 密钥和 API 密钥
- **JWT_SECRET**: 已生成64字符的安全密钥
- **ANON_KEY**: 已生成新的匿名访问密钥
- **SERVICE_ROLE_KEY**: 已生成新的服务角色密钥
- 这些密钥基于新的JWT_SECRET生成，确保安全性

### 3. 仪表板认证
- **DASHBOARD_USERNAME**: 已更改为 `admin`
- **DASHBOARD_PASSWORD**: 已设置为强密码 `Sup4b4se_D4shb04rd_2024!`

### 4. 其他安全密钥
- **SECRET_KEY_BASE**: 已生成新的加密密钥
- **VAULT_ENC_KEY**: 已设置32字符的加密密钥
- **POOLER_TENANT_ID**: 已设置为 `supabase-tenant-2024`

### 5. 日志分析密钥
- **LOGFLARE_PUBLIC_ACCESS_TOKEN**: 已生成新的公共访问令牌
- **LOGFLARE_PRIVATE_ACCESS_TOKEN**: 已生成新的私有访问令牌

## 需要您手动配置的项目

### 1. 邮件服务器配置
当前配置为Gmail示例，您需要根据实际情况修改：

```env
SMTP_ADMIN_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_SENDER_NAME=Supabase Admin
```

**Gmail配置步骤：**
1. 启用两步验证
2. 生成应用专用密码
3. 将应用密码填入 `SMTP_PASS`

**其他邮件服务商：**
- **AWS SES**: 推荐用于生产环境
- **SendGrid**: 易于配置
- **Mailgun**: 功能丰富

### 2. 域名和URL配置
如果部署到生产环境，需要修改：

```env
SITE_URL=https://yourdomain.com
API_EXTERNAL_URL=https://yourdomain.com
SUPABASE_PUBLIC_URL=https://yourdomain.com
```

### 3. S3存储配置（可选）
如需使用S3存储，在docker-compose.yml中添加：

```yaml
storage:
  environment:
    STORAGE_BACKEND: s3
    GLOBAL_S3_BUCKET: your-bucket-name
    REGION: your-region
    AWS_ACCESS_KEY_ID: your-access-key
    AWS_SECRET_ACCESS_KEY: your-secret-key
```

## 启动服务

1. 确保Docker和Docker Compose已安装
2. 在docker目录下运行：

```bash
# 拉取最新镜像
docker compose pull

# 启动服务（后台运行）
docker compose up -d

# 检查服务状态
docker compose ps
```

## 访问服务

- **Supabase Studio**: http://localhost:8000
  - 用户名: `admin`
  - 密码: `Sup4b4se_D4shb04rd_2024!`

- **API端点**:
  - REST API: http://localhost:8000/rest/v1/
  - Auth API: http://localhost:8000/auth/v1/
  - Storage API: http://localhost:8000/storage/v1/
  - Realtime: http://localhost:8000/realtime/v1/

- **数据库连接**:
  - 会话连接: `postgres://postgres.supabase-tenant-2024:Sup4b4se_P0stgr3s_P4ssw0rd_2024!@localhost:5432/postgres`
  - 池化连接: `postgres://postgres.supabase-tenant-2024:Sup4b4se_P0stgr3s_P4ssw0rd_2024!@localhost:6543/postgres`

## 安全建议

1. **定期更新密码**: 建议每3-6个月更换一次密码
2. **使用HTTPS**: 生产环境必须使用SSL证书
3. **防火墙配置**: 只开放必要的端口
4. **备份策略**: 定期备份数据库和配置文件
5. **监控日志**: 定期检查访问日志和错误日志
6. **密钥管理**: 考虑使用专业的密钥管理服务

## 故障排除

如果服务无法启动，请检查：
1. Docker服务是否正常运行
2. 端口是否被占用
3. 查看容器日志: `docker compose logs [service-name]`
4. 确保.env文件格式正确，无多余空格

## 重要提醒

⚠️ **请妥善保管这些密码和密钥，不要将它们提交到版本控制系统中！**

⚠️ **在生产环境中，强烈建议使用专业的密钥管理服务来存储这些敏感信息。**
