version: "3.8"

services:
  studio:
    build:
      context: ..
      dockerfile: apps/studio/Dockerfile
      target: dev
    ports:
      - 8082:8082
  mail:
    container_name: supabase-mail
    image: inbucket/inbucket:3.0.3
    ports:
      - '2500:2500' # SMTP
      - '9000:9000' # web interface
      - '1100:1100' # POP3
  auth:
    environment:
      - GOTRUE_SMTP_USER=
      - GOTRUE_SMTP_PASS=
  meta:
    ports:
      - 5555:8080
  db:
    restart: 'no'
    volumes:
      # Always use a fresh database when developing
      - /var/lib/postgresql/data
      # Seed data should be inserted last (alphabetical order)
      - ./dev/data.sql:/docker-entrypoint-initdb.d/seed.sql
  storage:
    volumes:
      - /var/lib/storage
